const HeroSection = () => {
  const scrollToKitchens = () => {
    document.getElementById('kitchens')?.scrollIntoView({ behavior: 'smooth' });
  };

  return (
    <section id="home" className="hero-section min-h-screen flex items-center relative">
      <div className="container mx-auto px-6 py-20 w-full relative z-10">
        <div className="max-w-4xl">
          <h1 className="text-4xl md:text-6xl font-bold text-white mb-6 leading-tight animate-fade-in">
            مطابخ تفوق التوقعات، وخزائن بتصاميم لا تُنسى
          </h1>
          <p className="text-xl text-gray-100 mb-8 leading-relaxed animate-fade-in-delay">
            نحول مساحات منزلك إلى تحف فنية تجمع بين الجمال والوظيفة بلمسة من الإبداع والحرفية العالية
          </p>
          <div className="flex flex-wrap gap-4 animate-fade-in-delay-2">
            <button
              onClick={scrollToKitchens}
              className="bg-primary hover:bg-blue-600 text-white px-8 py-4 rounded-button text-lg font-medium transition-all duration-300 transform hover:scale-105 hover:shadow-lg whitespace-nowrap"
            >
              شاهد تصاميمنا
            </button>
            <button
              onClick={() => document.getElementById('contact')?.scrollIntoView({ behavior: 'smooth' })}
              className="bg-white/10 backdrop-blur-sm border border-white/20 hover:bg-white/20 text-white px-8 py-4 rounded-button text-lg font-medium transition-all duration-300 transform hover:scale-105 whitespace-nowrap"
            >
              تواصل معنا
            </button>
          </div>
        </div>
      </div>

      {/* Floating Elements */}
      <div className="absolute bottom-10 left-1/2 transform -translate-x-1/2 text-white animate-bounce">
        <i className="ri-arrow-down-line text-2xl"></i>
      </div>
    </section>
  );
};

export default HeroSection;
