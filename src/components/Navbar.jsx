import { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { SOCIAL_CONFIG } from '../config/env'

const Navbar = () => {
  const [isScrolled, setIsScrolled] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  const [socialMedia, setSocialMedia] = useState([
    { platform: 'twitter', url: SOCIAL_CONFIG.twitter, icon: 'ri-twitter-line' },
    { platform: 'snapchat', url: SOCIAL_CONFIG.snapchat, icon: 'ri-snapchat-line' },
    { platform: 'instagram', url: SOCIAL_CONFIG.instagram, icon: 'ri-instagram-line' },
    { platform: 'whatsapp', url: SOCIAL_CONFIG.whatsapp, icon: 'ri-whatsapp-line' },
    { platform: 'tiktok', url: SOCIAL_CONFIG.tiktok, icon: 'ri-tiktok-line' }
  ])

  useEffect(() => {
    const savedFooterData = localStorage.getItem('admin_footer_data')
    if (savedFooterData) {
      const footerData = JSON.parse(savedFooterData)
      setSocialMedia(footerData.socialMedia)
    }
  }, [])

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 50);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  const closeMobileMenu = () => {
    setIsMobileMenuOpen(false);
  };

  return (
    <nav className={`fixed w-full z-50 transition-all duration-500 ${
      isScrolled
        ? 'bg-gradient-to-r from-gray-900/95 to-gray-800/95 backdrop-blur-lg shadow-2xl border-b border-white/10'
        : 'glass-nav'
    }`}>
      <div className="container mx-auto px-6 py-4">
        <div className="flex items-center justify-between">
          {/* Enhanced Logo */}
          <div className="group cursor-pointer">
            <div className="font-['Pacifico'] text-white text-3xl bg-gradient-to-r from-blue-400 via-purple-500 to-pink-500 bg-clip-text text-transparent hover:scale-110 transition-all duration-300 drop-shadow-lg">
              Expert Wonders
            </div>
            <div className="h-0.5 bg-gradient-to-r from-blue-400 to-purple-500 scale-x-0 group-hover:scale-x-100 transition-transform duration-300 origin-left"></div>
          </div>

          {/* Enhanced Desktop Navigation */}
          <div className="hidden lg:flex items-center space-x-8 rtl:space-x-reverse">
            {[
              { href: "#home", text: "الرئيسية", icon: "ri-home-4-line" },
              { href: "#why-us", text: "لماذا نحن", icon: "ri-star-line" },
              { href: "#kitchens", text: "المطابخ", icon: "ri-restaurant-line" },
              { href: "#cabinets", text: "الخزائن", icon: "ri-archive-line" },
              { href: "#contact", text: "تواصل معنا", icon: "ri-phone-line" }
            ].map((item, index) => (
              <a
                key={index}
                href={item.href}
                className="group relative px-4 py-2 text-white font-medium transition-all duration-300 hover:text-transparent hover:bg-gradient-to-r hover:from-blue-400 hover:to-purple-500 hover:bg-clip-text rounded-lg"
              >
                <div className="flex items-center space-x-2 rtl:space-x-reverse">
                  <i className={`${item.icon} text-sm opacity-0 group-hover:opacity-100 transition-opacity duration-300`}></i>
                  <span>{item.text}</span>
                </div>
                <div className="absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-blue-400 to-purple-500 group-hover:w-full transition-all duration-300"></div>
                <div className="absolute inset-0 bg-white/5 rounded-lg scale-0 group-hover:scale-100 transition-transform duration-300 -z-10"></div>
              </a>
            ))}
          </div>

          {/* Enhanced Social Media Icons & Admin Button */}
          <div className="hidden md:flex items-center space-x-3 rtl:space-x-reverse">
            {socialMedia.map((social, index) => {
              const getGradientAndHover = (platform) => {
                const styles = {
                  instagram: { gradient: "from-purple-500 to-pink-500", hoverColor: "hover:shadow-purple-500/50" },
                  tiktok: { gradient: "from-gray-800 to-black", hoverColor: "hover:shadow-gray-800/50" },
                  snapchat: { gradient: "from-yellow-400 to-yellow-500", hoverColor: "hover:shadow-yellow-400/50" },
                  whatsapp: { gradient: "from-green-500 to-green-600", hoverColor: "hover:shadow-green-500/50" },
                  twitter: { gradient: "from-blue-400 to-blue-600", hoverColor: "hover:shadow-blue-500/50" }
                }
                return styles[platform] || { gradient: "from-gray-500 to-gray-600", hoverColor: "hover:shadow-gray-500/50" }
              }
              const { gradient, hoverColor } = getGradientAndHover(social.platform)

              return (
                <a
                  key={index}
                  href={social.url}
                  className={`relative w-11 h-11 rounded-xl bg-white/10 backdrop-blur-sm flex items-center justify-center hover:scale-110 transition-all duration-300 group border border-white/20 ${hoverColor} hover:shadow-lg`}
                  title={social.platform}
                >
                  <i className={`${social.icon} text-white text-lg group-hover:scale-110 transition-transform duration-300`}></i>
                  <div className={`absolute inset-0 bg-gradient-to-r ${gradient} rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300 -z-10`}></div>
                  <div className="absolute inset-0 bg-white/20 rounded-xl scale-0 group-hover:scale-100 transition-transform duration-300 -z-20"></div>
                </a>
              )
            })}

            {/* Admin Panel Button */}
        { /*   <Link
              to="/admin"
              className="relative w-11 h-11 rounded-xl bg-white/10 backdrop-blur-sm flex items-center justify-center hover:scale-110 transition-all duration-300 group border border-white/20 hover:shadow-blue-500/50 hover:shadow-lg ml-2"
              title="لوحة التحكم"
            >
              <i className="ri-admin-line text-white text-lg group-hover:scale-110 transition-transform duration-300"></i>
              <div className="absolute inset-0 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300 -z-10"></div>
              <div className="absolute inset-0 bg-white/20 rounded-xl scale-0 group-hover:scale-100 transition-transform duration-300 -z-20"></div>
            </Link> */}
          </div>

          {/* Enhanced Mobile Menu Button */}
          <div
            className="lg:hidden relative w-12 h-12 flex items-center justify-center text-white cursor-pointer bg-white/10 backdrop-blur-sm rounded-xl border border-white/20 hover:bg-white/20 transition-all duration-300 group"
            onClick={toggleMobileMenu}
          >
            <div className="relative">
              <i className={`ri-menu-line text-xl transition-all duration-300 ${isMobileMenuOpen ? 'rotate-90 opacity-0' : 'rotate-0 opacity-100'}`}></i>
              <i className={`ri-close-line text-xl absolute inset-0 transition-all duration-300 ${isMobileMenuOpen ? 'rotate-0 opacity-100' : '-rotate-90 opacity-0'}`}></i>
            </div>
            <div className="absolute inset-0 bg-gradient-to-r from-blue-500 to-purple-500 rounded-xl opacity-0 group-hover:opacity-20 transition-opacity duration-300"></div>
          </div>
        </div>

        {/* Enhanced Mobile Menu */}
        <div
          className={`lg:hidden mt-6 bg-white/95 backdrop-blur-xl rounded-2xl shadow-2xl absolute left-4 right-4 top-20 border border-white/30 transition-all duration-500 overflow-hidden ${
            isMobileMenuOpen
              ? 'opacity-100 translate-y-0 scale-100'
              : 'opacity-0 -translate-y-4 scale-95 pointer-events-none'
          }`}
        >
          <div className="p-6">
            <div className="flex flex-col space-y-2">
              {[
                { href: "#home", text: "الرئيسية", icon: "ri-home-4-line", color: "from-blue-500 to-blue-600" },
                { href: "#why-us", text: "لماذا نحن", icon: "ri-star-line", color: "from-purple-500 to-purple-600" },
                { href: "#kitchens", text: "المطابخ", icon: "ri-restaurant-line", color: "from-green-500 to-green-600" },
                { href: "#cabinets", text: "الخزائن", icon: "ri-archive-line", color: "from-orange-500 to-orange-600" },
                { href: "#contact", text: "تواصل معنا", icon: "ri-phone-line", color: "from-pink-500 to-pink-600" }
              ].map((item, index) => (
                <a
                  key={index}
                  href={item.href}
                  className="group relative text-gray-800 font-medium py-4 px-4 rounded-xl transition-all duration-300 hover:bg-gradient-to-r hover:from-gray-50 hover:to-gray-100 border border-transparent hover:border-gray-200"
                  onClick={closeMobileMenu}
                  style={{ animationDelay: `${index * 100}ms` }}
                >
                  <div className="flex items-center space-x-3 rtl:space-x-reverse">
                    <div className={`w-10 h-10 rounded-lg bg-gradient-to-r ${item.color} flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300`}>
                      <i className={`${item.icon} text-white text-lg`}></i>
                    </div>
                    <span className="text-lg group-hover:text-gray-900 transition-colors duration-300">{item.text}</span>
                  </div>
                  <div className="absolute left-0 bottom-0 w-0 h-0.5 bg-gradient-to-r from-blue-500 to-purple-500 group-hover:w-full transition-all duration-300"></div>
                </a>
              ))}

              {/* Admin Panel Link for Mobile */}
              <Link
                to="/admin"
                className="group relative text-gray-800 font-medium py-4 px-4 rounded-xl transition-all duration-300 hover:bg-gradient-to-r hover:from-gray-50 hover:to-gray-100 border border-transparent hover:border-gray-200"
                onClick={closeMobileMenu}
                style={{ animationDelay: '500ms' }}
              >
                <div className="flex items-center space-x-3 rtl:space-x-reverse">
                  <div className="w-10 h-10 rounded-lg bg-gradient-to-r from-indigo-500 to-blue-600 flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300">
                    <i className="ri-admin-line text-white text-lg"></i>
                  </div>
                  <span className="text-lg group-hover:text-gray-900 transition-colors duration-300">لوحة التحكم</span>
                </div>
                <div className="absolute left-0 bottom-0 w-0 h-0.5 bg-gradient-to-r from-blue-500 to-purple-500 group-hover:w-full transition-all duration-300"></div>
              </Link>
            </div>

            {/* Enhanced Mobile Social Media */}
            <div className="border-t border-gray-200/50 pt-6 mt-6">
              <div className="text-center mb-4">
                <p className="text-gray-600 text-sm font-medium mb-2">تابعنا على</p>
                <div className="w-16 h-0.5 bg-gradient-to-r from-blue-500 to-purple-500 mx-auto rounded-full"></div>
              </div>
              <div className="flex justify-center space-x-4 rtl:space-x-reverse">
                {socialMedia.map((social, index) => {
                  const getGradientAndShadow = (platform) => {
                    const styles = {
                      instagram: { gradient: "from-purple-500 to-pink-500", shadow: "shadow-purple-500/30" },
                      tiktok: { gradient: "from-gray-800 to-black", shadow: "shadow-gray-800/30" },
                      snapchat: { gradient: "from-yellow-400 to-yellow-500", shadow: "shadow-yellow-400/30" },
                      whatsapp: { gradient: "from-green-500 to-green-600", shadow: "shadow-green-500/30" },
                      twitter: { gradient: "from-blue-400 to-blue-600", shadow: "shadow-blue-500/30" }
                    }
                    return styles[platform] || { gradient: "from-gray-500 to-gray-600", shadow: "shadow-gray-500/30" }
                  }
                  const { gradient, shadow } = getGradientAndShadow(social.platform)

                  return (
                    <a
                      key={index}
                      href={social.url}
                      className={`relative w-12 h-12 rounded-xl bg-gradient-to-r ${gradient} flex items-center justify-center hover:scale-110 transition-all duration-300 ${shadow} hover:shadow-lg group`}
                      title={social.platform}
                      style={{ animationDelay: `${(index + 5) * 100}ms` }}
                    >
                      <i className={`${social.icon} text-white text-lg group-hover:scale-110 transition-transform duration-300`}></i>
                      <div className="absolute inset-0 bg-white/20 rounded-xl scale-0 group-hover:scale-100 transition-transform duration-300"></div>
                    </a>
                  )
                })}
              </div>
            </div>
          </div>
        </div>
      </div>
    </nav>
  );
};

export default Navbar;
